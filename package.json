{"name": "@teable/teable", "version": "1.9.0", "license": "AGPL-3.0", "private": true, "homepage": "https://github.com/teableio/teable", "repository": {"type": "git", "url": "https://github.com/teableio/teable"}, "author": {"name": "tea artist", "url": "https://github.com/tea-artist"}, "keywords": ["teable", "database"], "workspaces": ["apps/*", "packages/*", "plugins", "!apps/electron"], "scripts": {"clean:global-cache": "rimraf ./.cache", "deps:check": "pnpm --package=npm-check-updates@latest dlx npm-check-updates --configFileName .ncurc.yml --workspaces --root --mergeConfig", "deps:update": "pnpm --package=npm-check-updates@latest dlx npm-check-updates --configFileName .ncurc.yml -u --workspaces --root --mergeConfig", "build:packages": "pnpm -r -F './packages/**' build", "g:build": "pnpm -r run build", "g:build-changed": "pnpm -r -F '...[origin/main]' build", "g:check-dist": "pnpm -r --parallel check-dist", "g:clean": "pnpm clean:global-cache && pnpm -r run clean", "g:fix-all-files": "pnpm -r fix-all-files", "g:lint": "pnpm -r --parallel lint --color", "g:lint-staged-files": "lint-staged --allow-empty", "g:lint-styles": "pnpm -r lint-styles --color", "g:test": "pnpm g:test-e2e && pnpm g:test-unit", "g:test-e2e": "pnpm -r test-e2e", "g:test-e2e-cover": "pnpm -r test-e2e-cover", "g:test-unit": "pnpm -r --parallel test-unit", "g:test-unit-cover": "pnpm -r --parallel test-unit-cover", "g:typecheck": "pnpm -r --parallel typecheck", "generate-openapi-types": "node scripts/generate-openapi-types.mjs", "install:playwright": "playwright install", "install:husky": "node .husky/install.mjs", "nuke:node_modules": "pnpm -r exec -- rm -fr node_modules", "publish:beta:prerelease": "node ./scripts/publish.mjs prerelease beta", "publish:beta:patch": "node ./scripts/publish.mjs prepatch beta", "publish:beta:minor": "node ./scripts/publish.mjs preminor beta", "publish:beta:major": "node ./scripts/publish.mjs premajor beta", "publish:next:patch": "node ./scripts/publish.mjs patch next", "publish:next:minor": "node ./scripts/publish.mjs minor next", "publish:next:major": "node ./scripts/publish.mjs major next", "publish:latest:patch": "node ./scripts/publish.mjs patch latest", "publish:latest:minor": "node ./scripts/publish.mjs minor latest", "publish:latest:major": "node ./scripts/publish.mjs major latest", "prepare": "run-s install:husky", "run:plugin": "pnpm -F '@teable/plugin' dev"}, "dependencies": {"cross-env": "7.0.3"}, "devDependencies": {"@commitlint/cli": "19.2.1", "@commitlint/config-conventional": "19.1.0", "@teable/eslint-config-bases": "workspace:^", "@types/shell-quote": "1.7.5", "eslint": "8.57.0", "husky": "9.0.11", "lint-staged": "15.2.2", "npm-run-all2": "6.1.2", "openapi-typescript": "6.7.5", "prettier": "3.2.5", "rimraf": "5.0.5", "shell-quote": "1.8.1", "typescript": "5.4.3", "zx": "7.2.3"}}